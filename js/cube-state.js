/**
 * Clean Cube State Management
 * Handles the logical state of the Rubik's cube using string representation
 * Independent of visual orientation
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        // Color mappings - using standard cube orientation
        // White on top (U), Green on front (F), Red on right (R)
        this.colors = {
            'U': 'white',   // Up face must be white
            'D': 'yellow',  // Down face must be yellow
            'F': 'green',   // Front face must be green
            'B': 'blue',    // Back face must be blue
            'R': 'red',     // Right face must be red
            'L': 'orange'   // Left face must be orange
        };

        // Map colors to characters
        this.colorToChar = {
            'white': 'W',   // Up center
            'yellow': 'Y',  // Down center
            'green': 'G',   // Front center
            'blue': 'B',    // Back center
            'red': 'R',     // Right center
            'orange': 'O'   // Left center
        };

        this.charToColor = {
            'W': 'white',
            'Y': 'yellow',
            'G': 'green',
            'B': 'blue', 
            'R': 'red',
            'O': 'orange'
        };

        // Initialize solved cube string
        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });

        console.log(`Initialized ${this.size}x${this.size} cube:`, this.cubeString);
    }

    // Convert string to face dictionary for easier manipulation
    stringToFaces() {
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    const color = this.charToColor[char] || 'gray';
                    faces[face][row][col] = color;
                }
            }
        });

        return faces;
    }

    // Convert face dictionary back to string
    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    const color = faces[face][row][col];
                    const char = this.colorToChar[color] || 'W';
                    result += char;
                }
            }
        });

        return result;
    }

    // Rotate a face array 90 degrees while preserving center for 3x3
    rotateFaceArray(faceArray, clockwise = true) {
        const size = this.size;
        const temp = faceArray.map(row => [...row]);
        const centerValue = (size === 3) ? temp[1][1] : null; // Save center for 3x3

        if (clockwise) {
            // Clockwise: (i,j) -> (j, size-1-i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    // Skip center for 3x3
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[j][size - 1 - i] = temp[i][j];
                }
            }
        } else {
            // Counterclockwise: (i,j) -> (size-1-j, i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    // Skip center for 3x3
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[size - 1 - j][i] = temp[i][j];
                }
            }
        }

        // Restore center for 3x3
        if (size === 3) {
            faceArray[1][1] = centerValue;
        }
    }

    // Get column from face array
    getColumn(faceArray, colIndex) {
        return faceArray.map(row => row[colIndex]);
    }

    // Set column in face array
    setColumn(faceArray, colIndex, values) {
        for (let i = 0; i < faceArray.length; i++) {
            faceArray[i][colIndex] = values[i];
        }
    }

    // Validate cube state integrity
    validateCubeState() {
        // For 3x3 cubes, validate center positions and color counts
        if (this.size === 3) {
            const faces = this.stringToFaces();
            const expectedCenters = {
                'U': 'white',   // Up must be white
                'D': 'yellow',  // Down must be yellow
                'F': 'green',   // Front must be green
                'B': 'blue',    // Back must be blue
                'R': 'red',     // Right must be red
                'L': 'orange'   // Left must be orange
            };

            // Verify center positions
            for (const [face, expectedColor] of Object.entries(expectedCenters)) {
                if (faces[face][1][1] !== expectedColor) {
                    console.error(`Invalid center position: ${face} center is ${faces[face][1][1]}, should be ${expectedColor}`);
                    return false;
                }
            }
        }

        // Count each color
        const counts = {};
        for (let char of this.cubeString) {
            counts[char] = (counts[char] || 0) + 1;
        }
        
        // Each color must appear exactly size*size times
        const expectedCount = this.size * this.size;
        const isValid = ['W', 'Y', 'G', 'B', 'R', 'O'].every(color => counts[color] === expectedCount);
        
        if (!isValid) {
            console.error('Invalid color distribution:', counts);
            return false;
        }
        return true;
    }

    // Execute a face rotation move - COMPLETELY REWRITTEN WITH CORRECT LOGIC
    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);

        // Use position-based approach for accuracy
        this.rotateFacePositions(face, clockwise);
        this.updateAdjacentPositions(face, clockwise);

        console.log('Move completed, new string:', this.cubeString.substring(0, 20) + '...');
        return this.cubeString;
    }

    // Rotate face itself using position indices
    rotateFacePositions(face, clockwise = true) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const stickersPerFace = this.size * this.size;
        const startPos = faceIndex * stickersPerFace;

        // Get current face stickers
        const faceStickers = [];
        for (let i = 0; i < stickersPerFace; i++) {
            faceStickers[i] = this.cubeString[startPos + i];
        }

        // Create rotation mapping for 3x3 - CORRECTED
        // For clockwise: position i gets value from rotationMap[i]
        const rotationMap = clockwise ?
            [6, 3, 0, 7, 4, 1, 8, 5, 2] :  // Clockwise: 0←6, 1←3, 2←0, 3←7, 4←4, 5←1, 6←8, 7←5, 8←2
            [2, 5, 8, 1, 4, 7, 0, 3, 6];   // Counterclockwise: 0←2, 1←5, 2←8, 3←1, 4←4, 5←7, 6←0, 7←3, 8←6

        // Apply rotation (preserve center for 3x3)
        const rotated = [...faceStickers];
        for (let i = 0; i < 9; i++) {
            if (this.size === 3 && i === 4) continue; // Skip center
            rotated[i] = faceStickers[rotationMap[i]];
        }

        // Set rotated face back
        for (let i = 0; i < stickersPerFace; i++) {
            const arr = this.cubeString.split('');
            arr[startPos + i] = rotated[i];
            this.cubeString = arr.join('');
        }
    }

    // Update adjacent positions using correct global position mapping
    updateAdjacentPositions(face, clockwise = true) {
        // Global position mapping: U(0-8), R(9-17), F(18-26), D(27-35), L(36-44), B(45-53)
        // Based on the working manual implementation - CORRECTED CYCLES
        const positionCycles = {
            'U': [
                [9, 10, 11],   // R top row
                [18, 19, 20],  // F top row
                [36, 37, 38],  // L top row
                [45, 46, 47]   // B top row
            ],
            'D': [
                [42, 43, 44],  // L bottom row
                [24, 25, 26],  // F bottom row
                [15, 16, 17],  // R bottom row
                [51, 52, 53]   // B bottom row
            ],
            'F': [
                [44, 41, 38],  // L right column (reversed)
                [6, 7, 8],     // U bottom row
                [9, 12, 15],   // R left column
                [29, 28, 27]   // D top row (reversed)
            ],
            'B': [
                [17, 14, 11],  // R right column (reversed)
                [2, 1, 0],     // U top row (reversed)
                [36, 39, 42],  // L left column
                [33, 34, 35]   // D bottom row
            ],
            'R': [
                [26, 23, 20],  // F right column
                [35, 32, 29],  // D right column
                [53, 50, 47],  // B left column (reversed order)
                [2, 5, 8]      // U right column
            ],
            'L': [
                [18, 21, 24],  // F left column
                [27, 30, 33],  // D left column
                [53, 50, 47],  // B right column (reversed)
                [0, 3, 6]      // U left column
            ]
        };

        const cycle = positionCycles[face];
        if (!cycle) return;

        // Get current values
        const values = cycle.map(positions =>
            positions.map(pos => this.cubeString[pos])
        );

        // Rotate values
        if (clockwise) {
            const temp = values[0];
            for (let i = 0; i < values.length - 1; i++) {
                values[i] = values[i + 1];
            }
            values[values.length - 1] = temp;
        } else {
            const temp = values[values.length - 1];
            for (let i = values.length - 1; i > 0; i--) {
                values[i] = values[i - 1];
            }
            values[0] = temp;
        }

        // Set values back
        cycle.forEach((positions, i) => {
            positions.forEach((pos, j) => {
                const arr = this.cubeString.split('');
                arr[pos] = values[i][j];
                this.cubeString = arr.join('');
            });
        });
    }

    executeWideMove(wideFace, clockwise = true) {
        // Wide moves for 4x4 cubes (e.g., Rw, Uw, etc.)
        // A wide move affects the outer face and the adjacent inner layer

        if (this.size !== 4) {
            console.warn('Wide moves are only supported for 4x4 cubes');
            return this.cubeString;
        }

        const baseFace = wideFace.replace('w', ''); // Remove 'w' to get base face
        console.log(`Executing wide move: ${wideFace}${clockwise ? '' : "'"} (base: ${baseFace})`);

        // For 4x4 cubes, a wide move is equivalent to:
        // 1. The outer layer move (regular face move)
        // 2. The inner layer move (slice move)

        // Execute the outer layer move
        this.executeMove(baseFace, clockwise);

        // Execute the inner layer move
        this.executeSliceMove(baseFace, clockwise);

        console.log('Wide move completed');
        return this.cubeString;
    }

    executeSliceMove(face, clockwise = true) {
        // Execute a slice move (inner layer) for 4x4 cubes
        if (this.size !== 4) return;

        console.log(`Executing slice move for face: ${face}${clockwise ? '' : "'"}`);

        const faces = this.stringToFaces();

        // For slice moves, we need to rotate the inner layers
        // This is a simplified implementation - proper slice moves are complex
        // For now, we'll implement basic slice logic for the inner two layers

        this.updateAdjacentSlices(faces, face, clockwise);

        // Convert back to string
        this.cubeString = this.facesToString(faces);

        return this.cubeString;
    }

    updateAdjacentSlices(faces, face, clockwise) {
        // Update adjacent slices for wide moves
        // This affects the inner layers (indices 1 and 2 for 4x4)
        const size = this.size;

        // Similar to updateAdjacentFaces but for inner layers
        const sliceMappings = {
            'U': {
                faces: ['F', 'R', 'B', 'L'],
                getters: [
                    () => faces.F[1].slice(),
                    () => faces.R[1].slice(),
                    () => faces.B[1].slice(),
                    () => faces.L[1].slice()
                ],
                setters: [
                    (vals) => faces.F[1] = vals.slice(),
                    (vals) => faces.R[1] = vals.slice(),
                    (vals) => faces.B[1] = vals.slice(),
                    (vals) => faces.L[1] = vals.slice()
                ]
            },
            'D': {
                faces: ['F', 'L', 'B', 'R'],
                getters: [
                    () => faces.F[2].slice(),
                    () => faces.L[2].slice(),
                    () => faces.B[2].slice(),
                    () => faces.R[2].slice()
                ],
                setters: [
                    (vals) => faces.F[2] = vals.slice(),
                    (vals) => faces.L[2] = vals.slice(),
                    (vals) => faces.B[2] = vals.slice(),
                    (vals) => faces.R[2] = vals.slice()
                ]
            },
            // Add other faces as needed...
        };

        const mapping = sliceMappings[face];
        if (!mapping) return;

        // Get current slice values
        const slices = mapping.getters.map(getter => getter());

        // Rotate slices
        if (clockwise) {
            const temp = slices[0];
            for (let i = 0; i < 3; i++) {
                slices[i] = slices[i + 1];
            }
            slices[3] = temp;
        } else {
            const temp = slices[3];
            for (let i = 3; i > 0; i--) {
                slices[i] = slices[i - 1];
            }
            slices[0] = temp;
        }

        // Set new slice values
        mapping.setters.forEach((setter, i) => setter(slices[i]));
    }

    // Update adjacent faces using position-based approach - FIXED IMPLEMENTATION
    updateAdjacentFaces(faces, face, clockwise) {
        // Convert face dictionary to string temporarily for position-based operations
        const tempString = this.facesToString(faces);
        const originalString = this.cubeString;
        this.cubeString = tempString;

        // Define adjacent cycles based on reference implementation (position-based)
        const adjacentCycles = {
            'F': [
                { face: 'U', positions: [6, 7, 8] },      // Bottom row of U
                { face: 'R', positions: [0, 3, 6] },      // Left column of R
                { face: 'D', positions: [2, 1, 0] },      // Top row of D (reversed)
                { face: 'L', positions: [8, 5, 2] }       // Right column of L (reversed)
            ],
            'B': [
                { face: 'U', positions: [2, 1, 0] },      // Top row of U (reversed)
                { face: 'L', positions: [0, 3, 6] },      // Left column of L
                { face: 'D', positions: [6, 7, 8] },      // Bottom row of D
                { face: 'R', positions: [8, 5, 2] }       // Right column of R (reversed)
            ],
            'R': [
                { face: 'U', positions: [2, 5, 8] },      // Right column of U
                { face: 'B', positions: [6, 3, 0] },      // Left column of B (reversed)
                { face: 'D', positions: [2, 5, 8] },      // Right column of D
                { face: 'F', positions: [2, 5, 8] }       // Right column of F
            ],
            'L': [
                { face: 'U', positions: [0, 3, 6] },      // Left column of U
                { face: 'F', positions: [0, 3, 6] },      // Left column of F
                { face: 'D', positions: [0, 3, 6] },      // Left column of D
                { face: 'B', positions: [8, 5, 2] }       // Right column of B (reversed)
            ],
            'U': [
                { face: 'F', positions: [0, 1, 2] },      // Top row of F
                { face: 'L', positions: [0, 1, 2] },      // Top row of L
                { face: 'B', positions: [0, 1, 2] },      // Top row of B
                { face: 'R', positions: [0, 1, 2] }       // Top row of R
            ],
            'D': [
                { face: 'F', positions: [6, 7, 8] },      // Bottom row of F
                { face: 'R', positions: [6, 7, 8] },      // Bottom row of R
                { face: 'B', positions: [6, 7, 8] },      // Bottom row of B
                { face: 'L', positions: [6, 7, 8] }       // Bottom row of L
            ]
        };

        const cycle = adjacentCycles[face];
        if (!cycle) {
            this.cubeString = originalString;
            return;
        }

        // Get current values from all positions in the cycle
        const values = cycle.map(item =>
            item.positions.map(pos => {
                const globalPos = this.facePosToPosition(item.face, pos);
                return this.cubeString[globalPos];
            })
        );

        // Rotate values in the cycle
        if (clockwise) {
            // Clockwise: move values forward in cycle
            const temp = values[0];
            for (let i = 0; i < values.length - 1; i++) {
                values[i] = values[i + 1];
            }
            values[values.length - 1] = temp;
        } else {
            // Counterclockwise: move values backward in cycle
            const temp = values[values.length - 1];
            for (let i = values.length - 1; i > 0; i--) {
                values[i] = values[i - 1];
            }
            values[0] = temp;
        }

        // Set rotated values back
        cycle.forEach((item, i) => {
            item.positions.forEach((pos, j) => {
                const globalPos = this.facePosToPosition(item.face, pos);
                const arr = this.cubeString.split('');
                arr[globalPos] = values[i][j];
                this.cubeString = arr.join('');
            });
        });

        // Convert back to faces
        const newFaces = this.stringToFaces();
        Object.keys(faces).forEach(f => {
            faces[f] = newFaces[f];
        });

        // Restore original string reference
        this.cubeString = originalString;
    }

    // Helper method for position conversion
    facePosToPosition(face, localPos) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const faceIndex = faceOrder.indexOf(face);
        const stickersPerFace = this.size * this.size;
        return faceIndex * stickersPerFace + localPos;
    }

    // Generate random scramble with better move distribution
    generateScramble(moveCount = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const modifiers = ['', "'", '2']; // Normal, prime, double
        const scramble = [];
        let lastFace = '';

        for (let i = 0; i < moveCount; i++) {
            // Avoid consecutive moves on the same face
            let face;
            do {
                face = faces[Math.floor(Math.random() * faces.length)];
            } while (face === lastFace && faces.length > 1);

            const modifier = modifiers[Math.floor(Math.random() * modifiers.length)];
            scramble.push(face + modifier);
            lastFace = face;
        }

        return scramble.join(' ');
    }

    // Apply scramble sequence - COMPLETELY REWRITTEN for reliability
    applyScramble(scrambleString) {
        console.log('Applying scramble:', scrambleString);
        const moves = scrambleString.split(' ').filter(move => move.trim());

        for (let i = 0; i < moves.length; i++) {
            const move = moves[i].trim();
            if (!move) continue;

            console.log(`Applying move ${i + 1}/${moves.length}: ${move}`);

            try {
                // Parse the move
                const face = move[0].toUpperCase();
                let clockwise = true;
                let double = false;

                if (move.includes("'")) {
                    clockwise = false;
                } else if (move.includes('2')) {
                    double = true;
                }

                // Validate face
                if (!['U', 'D', 'F', 'B', 'R', 'L'].includes(face)) {
                    console.warn(`Invalid face in move: ${move}, skipping`);
                    continue;
                }

                // Apply the move
                if (double) {
                    // Apply twice for double moves
                    this.executeMove(face, true);
                    this.executeMove(face, true);
                    console.log(`Double move ${face}2 applied`);
                } else {
                    this.executeMove(face, clockwise);
                    console.log(`Move ${face}${clockwise ? '' : "'"} applied`);
                }

            } catch (error) {
                console.error(`Error applying move ${move}:`, error);
                // Continue with next move instead of failing completely
                continue;
            }
        }

        console.log('Scramble application completed');
        console.log('Final cube state:', this.cubeString.substring(0, 20) + '...');
        return this.cubeString;
    }

    // Apply solution sequence
    applySolution(solutionString) {
        console.log('Applying solution:', solutionString);
        
        // Get cube state before applying solution
        const initialState = this.cubeString;
        const kociembaString = this.getKociembaString();
        
        if (!kociembaString) {
            console.error('Cannot apply solution: cube is not in valid Kociemba orientation');
            return this.cubeString;
        }
        
        console.log('Cube state before solution:', {
            internal: initialState,
            kociemba: kociembaString
        });
        
        // Apply the solution moves
        this.applyScramble(solutionString);
        
        // Verify the cube is solved
        if (!this.isSolved()) {
            console.error('Solution did not solve the cube!');
            console.error('Initial state:', initialState);
            console.error('Kociemba string:', kociembaString);
            console.error('Solution applied:', solutionString);
            console.error('Final state:', this.cubeString);
        }
        
        return this.cubeString;
    }

    // Check if cube is solved
    isSolved() {
        const faces = this.stringToFaces();

        for (const face of ['U', 'R', 'F', 'D', 'L', 'B']) {
            const faceArray = faces[face];
            const firstColor = faceArray[0][0];

            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== firstColor) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // Change cube size
    changeSize(newSize) {
        if (![2, 3, 4].includes(newSize)) {
            console.error('Invalid cube size:', newSize);
            return;
        }

        this.size = newSize;
        this.resetToSolved();
        return this.cubeString;
    }

    // Get cube state as Kociemba format (for 3x3 only)
    getKociembaString() {
        if (this.size !== 3) {
            console.warn('Kociemba format only available for 3x3 cubes');
            return null;
        }

        // First verify that centers are in correct positions
        const faces = this.stringToFaces();

        // In standard Kociemba orientation:
        // White (U) on top, Green (F) on front
        // This means in our internal representation:
        // - The U (top) face center must be white
        // - The F (front) face center must be green
        // - The R (right) face center must be red
        // - The D (bottom) face center must be yellow
        // - The B (back) face center must be blue
        // - The L (left) face center must be orange
        
        const centerColors = {
            'U': faces.U[1][1], // Should be white
            'F': faces.F[1][1], // Should be green
            'R': faces.R[1][1], // Should be red
            'D': faces.D[1][1], // Should be yellow
            'B': faces.B[1][1], // Should be blue
            'L': faces.L[1][1]  // Should be orange
        };

        // Log current center positions for debugging
        console.log('Current centers:', centerColors);

        if (centerColors.U !== 'white' || 
            centerColors.F !== 'green' || 
            centerColors.R !== 'red' || 
            centerColors.D !== 'yellow' || 
            centerColors.B !== 'blue' || 
            centerColors.L !== 'orange') {
            console.error('Invalid cube orientation!');
            console.error('Centers must be: white on top (U), green on front (F), red on right (R)');
            console.error('Current centers:', centerColors);
            return null;
        }

        // Convert our WYGBRO format to Kociemba's URFDLB format
        // The mapping must be consistent with center colors:
        // W (white) -> U (up face)
        // Y (yellow) -> D (down face)
        // G (green) -> F (front face)
        // B (blue) -> B (back face)
        // R (red) -> R (right face)
        // O (orange) -> L (left face)
        const mapping = {
            'W': 'U',  // White center must be on U face
            'Y': 'D',  // Yellow center must be on D face
            'G': 'F',  // Green center must be on F face
            'B': 'B',  // Blue center must be on B face
            'R': 'R',  // Red center must be on R face
            'O': 'L'   // Orange center must be on L face
        };
        
        return this.cubeString.split('').map(c => mapping[c]).join('');
    }
}

// Export for use in other modules
window.CubeState = CubeState;
