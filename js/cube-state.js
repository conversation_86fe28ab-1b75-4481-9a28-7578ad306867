/**
 * Clean Cube State Management
 * Handles the logical state of the Rubik's cube using string representation
 * Independent of visual orientation
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        // Color mappings - using standard cube orientation
        // White on top (U), Green on front (F), Red on right (R)
        this.colors = {
            'U': 'white',   // Up face must be white
            'D': 'yellow',  // Down face must be yellow
            'F': 'green',   // Front face must be green
            'B': 'blue',    // Back face must be blue
            'R': 'red',     // Right face must be red
            'L': 'orange'   // Left face must be orange
        };

        // Map colors to characters
        this.colorToChar = {
            'white': 'W',   // Up center
            'yellow': 'Y',  // Down center
            'green': 'G',   // Front center
            'blue': 'B',    // Back center
            'red': 'R',     // Right center
            'orange': 'O'   // Left center
        };

        this.charToColor = {
            'W': 'white',
            'Y': 'yellow',
            'G': 'green',
            'B': 'blue', 
            'R': 'red',
            'O': 'orange'
        };

        // Initialize solved cube string
        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });

        console.log(`Initialized ${this.size}x${this.size} cube:`, this.cubeString);
    }

    // Convert string to face dictionary for easier manipulation
    stringToFaces() {
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    const color = this.charToColor[char] || 'gray';
                    faces[face][row][col] = color;
                }
            }
        });

        return faces;
    }

    // Convert face dictionary back to string
    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    const color = faces[face][row][col];
                    const char = this.colorToChar[color] || 'W';
                    result += char;
                }
            }
        });

        return result;
    }

    // Rotate a face array 90 degrees while preserving center for 3x3
    rotateFaceArray(faceArray, clockwise = true) {
        const size = this.size;
        const temp = faceArray.map(row => [...row]);
        const centerValue = (size === 3) ? temp[1][1] : null; // Save center for 3x3

        if (clockwise) {
            // Clockwise: (i,j) -> (j, size-1-i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    // Skip center for 3x3
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[j][size - 1 - i] = temp[i][j];
                }
            }
        } else {
            // Counterclockwise: (i,j) -> (size-1-j, i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    // Skip center for 3x3
                    if (size === 3 && i === 1 && j === 1) continue;
                    faceArray[size - 1 - j][i] = temp[i][j];
                }
            }
        }

        // Restore center for 3x3
        if (size === 3) {
            faceArray[1][1] = centerValue;
        }
    }

    // Get column from face array
    getColumn(faceArray, colIndex) {
        return faceArray.map(row => row[colIndex]);
    }

    // Set column in face array
    setColumn(faceArray, colIndex, values) {
        for (let i = 0; i < faceArray.length; i++) {
            faceArray[i][colIndex] = values[i];
        }
    }

    // Validate cube state integrity
    validateCubeState() {
        // For 3x3 cubes, validate center positions and color counts
        if (this.size === 3) {
            const faces = this.stringToFaces();
            const expectedCenters = {
                'U': 'white',   // Up must be white
                'D': 'yellow',  // Down must be yellow
                'F': 'green',   // Front must be green
                'B': 'blue',    // Back must be blue
                'R': 'red',     // Right must be red
                'L': 'orange'   // Left must be orange
            };

            // Verify center positions
            for (const [face, expectedColor] of Object.entries(expectedCenters)) {
                if (faces[face][1][1] !== expectedColor) {
                    console.error(`Invalid center position: ${face} center is ${faces[face][1][1]}, should be ${expectedColor}`);
                    return false;
                }
            }
        }

        // Count each color
        const counts = {};
        for (let char of this.cubeString) {
            counts[char] = (counts[char] || 0) + 1;
        }
        
        // Each color must appear exactly size*size times
        const expectedCount = this.size * this.size;
        const isValid = ['W', 'Y', 'G', 'B', 'R', 'O'].every(color => counts[color] === expectedCount);
        
        if (!isValid) {
            console.error('Invalid color distribution:', counts);
            return false;
        }
        return true;
    }

    // Execute a face rotation move
    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);

        const faces = this.stringToFaces();
        
        // Rotate the face itself
        this.rotateFaceArray(faces[face], clockwise);
        
        // Update adjacent faces based on the specific face being rotated
        this.updateAdjacentFaces(faces, face, clockwise);
        
        // Convert back to string
        this.cubeString = this.facesToString(faces);
        
        // Validate the new state
        if (!this.validateCubeState()) {
            console.error('Move resulted in invalid cube state:', face, clockwise);
            this.resetToSolved(); // Reset to prevent invalid state
            throw new Error('Invalid cube state detected after move');
        }
        
        console.log('Move completed, new string:', this.cubeString.substring(0, 20) + '...');
        return this.cubeString;
    }

    executeWideMove(wideFace, clockwise = true) {
        // Wide moves for 4x4 cubes (e.g., Rw, Uw, etc.)
        // A wide move affects the outer face and the adjacent inner layer

        if (this.size !== 4) {
            console.warn('Wide moves are only supported for 4x4 cubes');
            return this.cubeString;
        }

        const baseFace = wideFace.replace('w', ''); // Remove 'w' to get base face
        console.log(`Executing wide move: ${wideFace}${clockwise ? '' : "'"} (base: ${baseFace})`);

        // For 4x4 cubes, a wide move is equivalent to:
        // 1. The outer layer move (regular face move)
        // 2. The inner layer move (slice move)

        // Execute the outer layer move
        this.executeMove(baseFace, clockwise);

        // Execute the inner layer move
        this.executeSliceMove(baseFace, clockwise);

        console.log('Wide move completed');
        return this.cubeString;
    }

    executeSliceMove(face, clockwise = true) {
        // Execute a slice move (inner layer) for 4x4 cubes
        if (this.size !== 4) return;

        console.log(`Executing slice move for face: ${face}${clockwise ? '' : "'"}`);

        const faces = this.stringToFaces();

        // For slice moves, we need to rotate the inner layers
        // This is a simplified implementation - proper slice moves are complex
        // For now, we'll implement basic slice logic for the inner two layers

        this.updateAdjacentSlices(faces, face, clockwise);

        // Convert back to string
        this.cubeString = this.facesToString(faces);

        return this.cubeString;
    }

    updateAdjacentSlices(faces, face, clockwise) {
        // Update adjacent slices for wide moves
        // This affects the inner layers (indices 1 and 2 for 4x4)
        const size = this.size;

        // Similar to updateAdjacentFaces but for inner layers
        const sliceMappings = {
            'U': {
                faces: ['F', 'R', 'B', 'L'],
                getters: [
                    () => faces.F[1].slice(),
                    () => faces.R[1].slice(),
                    () => faces.B[1].slice(),
                    () => faces.L[1].slice()
                ],
                setters: [
                    (vals) => faces.F[1] = vals.slice(),
                    (vals) => faces.R[1] = vals.slice(),
                    (vals) => faces.B[1] = vals.slice(),
                    (vals) => faces.L[1] = vals.slice()
                ]
            },
            'D': {
                faces: ['F', 'L', 'B', 'R'],
                getters: [
                    () => faces.F[2].slice(),
                    () => faces.L[2].slice(),
                    () => faces.B[2].slice(),
                    () => faces.R[2].slice()
                ],
                setters: [
                    (vals) => faces.F[2] = vals.slice(),
                    (vals) => faces.L[2] = vals.slice(),
                    (vals) => faces.B[2] = vals.slice(),
                    (vals) => faces.R[2] = vals.slice()
                ]
            },
            // Add other faces as needed...
        };

        const mapping = sliceMappings[face];
        if (!mapping) return;

        // Get current slice values
        const slices = mapping.getters.map(getter => getter());

        // Rotate slices
        if (clockwise) {
            const temp = slices[0];
            for (let i = 0; i < 3; i++) {
                slices[i] = slices[i + 1];
            }
            slices[3] = temp;
        } else {
            const temp = slices[3];
            for (let i = 3; i > 0; i--) {
                slices[i] = slices[i - 1];
            }
            slices[0] = temp;
        }

        // Set new slice values
        mapping.setters.forEach((setter, i) => setter(slices[i]));
    }

    // Update adjacent faces when a face is rotated
    updateAdjacentFaces(faces, face, clockwise) {
        const size = this.size;

        // Define correct adjacent face relationships for each face rotation
        // Based on standard Rubik's cube mechanics - CORRECTED VERSION
        const adjacentMappings = {
            'U': {
                // U move clockwise: F-top → R-top → B-top → L-top → F-top
                faces: ['F', 'R', 'B', 'L'],
                getters: [
                    () => faces.F[0].slice(),
                    () => faces.R[0].slice(),
                    () => faces.B[0].slice(),
                    () => faces.L[0].slice()
                ],
                setters: [
                    (vals) => faces.F[0] = vals.slice(),
                    (vals) => faces.R[0] = vals.slice(),
                    (vals) => faces.B[0] = vals.slice(),
                    (vals) => faces.L[0] = vals.slice()
                ]
            },
            'D': {
                // D move clockwise: F-bottom → L-bottom → B-bottom → R-bottom → F-bottom
                faces: ['F', 'L', 'B', 'R'],
                getters: [
                    () => faces.F[size-1].slice(),
                    () => faces.L[size-1].slice(),
                    () => faces.B[size-1].slice(),
                    () => faces.R[size-1].slice()
                ],
                setters: [
                    (vals) => faces.F[size-1] = vals.slice(),
                    (vals) => faces.L[size-1] = vals.slice(),
                    (vals) => faces.B[size-1] = vals.slice(),
                    (vals) => faces.R[size-1] = vals.slice()
                ]
            },
            'F': {
                // F move clockwise: U-bottom → R-left → D-top(reversed) → L-right(reversed) → U-bottom
                faces: ['U', 'R', 'D', 'L'],
                getters: [
                    () => faces.U[size-1].slice(),
                    () => this.getColumn(faces.R, 0).slice(),
                    () => faces.D[0].slice().reverse(),
                    () => this.getColumn(faces.L, size-1).slice().reverse()
                ],
                setters: [
                    (vals) => faces.U[size-1] = vals.slice(),
                    (vals) => this.setColumn(faces.R, 0, vals.slice()),
                    (vals) => faces.D[0] = vals.slice().reverse(),
                    (vals) => this.setColumn(faces.L, size-1, vals.slice().reverse())
                ]
            },
            'B': {
                // B move clockwise: U-top(reversed) → L-left → D-bottom → R-right(reversed) → U-top
                faces: ['U', 'L', 'D', 'R'],
                getters: [
                    () => faces.U[0].slice().reverse(),
                    () => this.getColumn(faces.L, 0).slice(),
                    () => faces.D[size-1].slice(),
                    () => this.getColumn(faces.R, size-1).slice().reverse()
                ],
                setters: [
                    (vals) => faces.U[0] = vals.slice().reverse(),
                    (vals) => this.setColumn(faces.L, 0, vals.slice()),
                    (vals) => faces.D[size-1] = vals.slice(),
                    (vals) => this.setColumn(faces.R, size-1, vals.slice().reverse())
                ]
            },
            'R': {
                // R move clockwise: U-right → B-left(reversed) → D-right → F-right → U-right
                faces: ['U', 'B', 'D', 'F'],
                getters: [
                    () => this.getColumn(faces.U, size-1).slice(),
                    () => this.getColumn(faces.B, 0).slice().reverse(),
                    () => this.getColumn(faces.D, size-1).slice(),
                    () => this.getColumn(faces.F, size-1).slice()
                ],
                setters: [
                    (vals) => this.setColumn(faces.U, size-1, vals.slice()),
                    (vals) => this.setColumn(faces.B, 0, vals.slice().reverse()),
                    (vals) => this.setColumn(faces.D, size-1, vals.slice()),
                    (vals) => this.setColumn(faces.F, size-1, vals.slice())
                ]
            },
            'L': {
                // L move clockwise: U-left → F-left → D-left → B-right(reversed) → U-left
                faces: ['U', 'F', 'D', 'B'],
                getters: [
                    () => this.getColumn(faces.U, 0).slice(),
                    () => this.getColumn(faces.F, 0).slice(),
                    () => this.getColumn(faces.D, 0).slice(),
                    () => this.getColumn(faces.B, size-1).slice().reverse()
                ],
                setters: [
                    (vals) => this.setColumn(faces.U, 0, vals.slice()),
                    (vals) => this.setColumn(faces.F, 0, vals.slice()),
                    (vals) => this.setColumn(faces.D, 0, vals.slice()),
                    (vals) => this.setColumn(faces.B, size-1, vals.slice().reverse())
                ]
            }
        };

        const mapping = adjacentMappings[face];
        if (!mapping) return;

        // Get current edge values
        const edges = mapping.getters.map(getter => getter());

        // Rotate edges - CORRECTED LOGIC
        if (clockwise) {
            // Move edges clockwise: 0→1, 1→2, 2→3, 3→0
            const temp = edges[0];
            for (let i = 0; i < 3; i++) {
                edges[i] = edges[i + 1];
            }
            edges[3] = temp;
        } else {
            // Move edges counterclockwise: 0→3, 1→0, 2→1, 3→2
            const temp = edges[3];
            for (let i = 3; i > 0; i--) {
                edges[i] = edges[i - 1];
            }
            edges[0] = temp;
        }

        // Set new edge values
        mapping.setters.forEach((setter, i) => setter(edges[i]));
    }

    // Generate random scramble
    generateScramble(moves = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const scramble = [];

        for (let i = 0; i < moves; i++) {
            const face = faces[Math.floor(Math.random() * faces.length)];
            const clockwise = Math.random() > 0.5;
            scramble.push(face + (clockwise ? '' : "'"));
        }

        return scramble.join(' ');
    }

    // Apply scramble sequence
    applyScramble(scrambleString) {
        const moves = scrambleString.split(' ').filter(move => move.trim());
        
        console.log('Applying scramble:', scrambleString);
        
        // Record initial centers to verify they don't move
        const initialFaces = this.stringToFaces();
        const initialCenters = {
            'U': initialFaces.U[1][1],
            'F': initialFaces.F[1][1],
            'R': initialFaces.R[1][1],
            'D': initialFaces.D[1][1],
            'B': initialFaces.B[1][1],
            'L': initialFaces.L[1][1]
        };
        
        console.log('Initial centers:', initialCenters);

        moves.forEach(move => {
            const face = move[0];
            const clockwise = !move.includes("'");
            this.executeMove(face, clockwise);
            
            // Verify centers after each move
            const currentFaces = this.stringToFaces();
            const currentCenters = {
                'U': currentFaces.U[1][1],
                'F': currentFaces.F[1][1],
                'R': currentFaces.R[1][1],
                'D': currentFaces.D[1][1],
                'B': currentFaces.B[1][1],
                'L': currentFaces.L[1][1]
            };
            
            // Check if any centers moved
            const centersChanged = Object.entries(initialCenters).some(
                ([face, color]) => currentCenters[face] !== color
            );
            
            if (centersChanged) {
                console.error('Centers changed during move:', move);
                console.error('Initial centers:', initialCenters);
                console.error('Current centers:', currentCenters);
                throw new Error('Centers must stay fixed during moves');
            }
        });

        return this.cubeString;
    }

    // Apply solution sequence
    applySolution(solutionString) {
        console.log('Applying solution:', solutionString);
        
        // Get cube state before applying solution
        const initialState = this.cubeString;
        const kociembaString = this.getKociembaString();
        
        if (!kociembaString) {
            console.error('Cannot apply solution: cube is not in valid Kociemba orientation');
            return this.cubeString;
        }
        
        console.log('Cube state before solution:', {
            internal: initialState,
            kociemba: kociembaString
        });
        
        // Apply the solution moves
        this.applyScramble(solutionString);
        
        // Verify the cube is solved
        if (!this.isSolved()) {
            console.error('Solution did not solve the cube!');
            console.error('Initial state:', initialState);
            console.error('Kociemba string:', kociembaString);
            console.error('Solution applied:', solutionString);
            console.error('Final state:', this.cubeString);
        }
        
        return this.cubeString;
    }

    // Check if cube is solved
    isSolved() {
        const faces = this.stringToFaces();

        for (const face of ['U', 'R', 'F', 'D', 'L', 'B']) {
            const faceArray = faces[face];
            const firstColor = faceArray[0][0];

            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== firstColor) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // Change cube size
    changeSize(newSize) {
        if (![2, 3, 4].includes(newSize)) {
            console.error('Invalid cube size:', newSize);
            return;
        }

        this.size = newSize;
        this.resetToSolved();
        return this.cubeString;
    }

    // Get cube state as Kociemba format (for 3x3 only)
    getKociembaString() {
        if (this.size !== 3) {
            console.warn('Kociemba format only available for 3x3 cubes');
            return null;
        }

        // First verify that centers are in correct positions
        const faces = this.stringToFaces();

        // In standard Kociemba orientation:
        // White (U) on top, Green (F) on front
        // This means in our internal representation:
        // - The U (top) face center must be white
        // - The F (front) face center must be green
        // - The R (right) face center must be red
        // - The D (bottom) face center must be yellow
        // - The B (back) face center must be blue
        // - The L (left) face center must be orange
        
        const centerColors = {
            'U': faces.U[1][1], // Should be white
            'F': faces.F[1][1], // Should be green
            'R': faces.R[1][1], // Should be red
            'D': faces.D[1][1], // Should be yellow
            'B': faces.B[1][1], // Should be blue
            'L': faces.L[1][1]  // Should be orange
        };

        // Log current center positions for debugging
        console.log('Current centers:', centerColors);

        if (centerColors.U !== 'white' || 
            centerColors.F !== 'green' || 
            centerColors.R !== 'red' || 
            centerColors.D !== 'yellow' || 
            centerColors.B !== 'blue' || 
            centerColors.L !== 'orange') {
            console.error('Invalid cube orientation!');
            console.error('Centers must be: white on top (U), green on front (F), red on right (R)');
            console.error('Current centers:', centerColors);
            return null;
        }

        // Convert our WYGBRO format to Kociemba's URFDLB format
        // The mapping must be consistent with center colors:
        // W (white) -> U (up face)
        // Y (yellow) -> D (down face)
        // G (green) -> F (front face)
        // B (blue) -> B (back face)
        // R (red) -> R (right face)
        // O (orange) -> L (left face)
        const mapping = {
            'W': 'U',  // White center must be on U face
            'Y': 'D',  // Yellow center must be on D face
            'G': 'F',  // Green center must be on F face
            'B': 'B',  // Blue center must be on B face
            'R': 'R',  // Red center must be on R face
            'O': 'L'   // Orange center must be on L face
        };
        
        return this.cubeString.split('').map(c => mapping[c]).join('');
    }
}

// Export for use in other modules
window.CubeState = CubeState;
