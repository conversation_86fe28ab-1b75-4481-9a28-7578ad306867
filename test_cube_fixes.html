<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cube Fixes Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .test-result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        button { margin: 5px; padding: 10px; }
        pre { background: #f8f9fa; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Rubik's Cube Fixes Test</h1>
    
    <div class="test-section">
        <h2>3x3 Cube Move Test</h2>
        <button onclick="test3x3Moves()">Test 3x3 Face Moves</button>
        <button onclick="test3x3ScrambleAndSolve()">Test 3x3 Scramble & Solve</button>
        <div id="test3x3-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2x2 Cube Test</h2>
        <button onclick="test2x2Solver()">Test 2x2 Solver</button>
        <div id="test2x2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4x4 Cube Test</h2>
        <button onclick="test4x4Solver()">Test 4x4 Solver</button>
        <button onclick="test4x4WideMoves()">Test 4x4 Wide Moves</button>
        <div id="test4x4-result"></div>
    </div>

    <script src="js/cube-state.js"></script>
    <script>
        function logResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            element.appendChild(div);
        }

        function test3x3Moves() {
            const resultId = 'test3x3-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                logResult(resultId, 'Testing 3x3 cube moves...', 'info');
                
                const cube = new CubeState(3);
                const initialState = cube.cubeString;
                
                logResult(resultId, `Initial state: ${initialState.substring(0, 20)}...`, 'info');
                
                // Test basic moves
                cube.executeMove('R', true);
                logResult(resultId, 'R move executed', 'success');
                
                cube.executeMove('U', true);
                logResult(resultId, 'U move executed', 'success');
                
                cube.executeMove('R', false);
                logResult(resultId, "R' move executed", 'success');
                
                cube.executeMove('U', false);
                logResult(resultId, "U' move executed", 'success');
                
                // Check if we're back to solved state
                if (cube.cubeString === initialState) {
                    logResult(resultId, '✓ Move sequence R U R\' U\' returned to solved state!', 'success');
                } else {
                    logResult(resultId, '✗ Move sequence did not return to solved state', 'error');
                    logResult(resultId, `Final state: ${cube.cubeString.substring(0, 20)}...`, 'error');
                }
                
            } catch (error) {
                logResult(resultId, `Error: ${error.message}`, 'error');
            }
        }

        async function test3x3ScrambleAndSolve() {
            const resultId = 'test3x3-result';
            
            try {
                logResult(resultId, 'Testing 3x3 scramble and solve...', 'info');
                
                const cube = new CubeState(3);
                
                // Generate and apply scramble
                const scramble = cube.generateScramble(10);
                logResult(resultId, `Generated scramble: ${scramble}`, 'info');
                
                cube.applyScramble(scramble);
                logResult(resultId, 'Scramble applied', 'success');
                
                if (cube.isSolved()) {
                    logResult(resultId, '✗ Cube is still solved after scramble!', 'error');
                    return;
                }
                
                // Test solver
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    logResult(resultId, '✗ Could not convert to Kociemba format', 'error');
                    return;
                }
                
                logResult(resultId, `Kociemba string: ${kociembaString.substring(0, 20)}...`, 'info');
                
                // Try to solve via server
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.solution) {
                        logResult(resultId, `Solution: ${data.solution}`, 'success');
                        
                        // Apply solution
                        cube.applySolution(data.solution);
                        
                        if (cube.isSolved()) {
                            logResult(resultId, '✓ Cube solved successfully!', 'success');
                        } else {
                            logResult(resultId, '✗ Solution did not solve the cube', 'error');
                        }
                    } else {
                        logResult(resultId, '✗ No solution received', 'error');
                    }
                } else {
                    logResult(resultId, `✗ Server error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                logResult(resultId, `Error: ${error.message}`, 'error');
            }
        }

        async function test2x2Solver() {
            const resultId = 'test2x2-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                logResult(resultId, 'Testing 2x2 solver...', 'info');
                
                const cube = new CubeState(2);
                logResult(resultId, `2x2 cube initialized: ${cube.cubeString}`, 'info');
                
                // Test solver
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: cube.cubeString })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    logResult(resultId, `Server response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    logResult(resultId, `✗ Server error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                logResult(resultId, `Error: ${error.message}`, 'error');
            }
        }

        async function test4x4Solver() {
            const resultId = 'test4x4-result';
            document.getElementById(resultId).innerHTML = '';
            
            try {
                logResult(resultId, 'Testing 4x4 solver...', 'info');
                
                const cube = new CubeState(4);
                logResult(resultId, `4x4 cube initialized: ${cube.cubeString.substring(0, 30)}...`, 'info');
                
                // Test solver
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: cube.cubeString })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    logResult(resultId, `Server response: ${JSON.stringify(data, null, 2)}`, 'success');
                } else {
                    logResult(resultId, `✗ Server error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                logResult(resultId, `Error: ${error.message}`, 'error');
            }
        }

        function test4x4WideMoves() {
            const resultId = 'test4x4-result';
            
            try {
                logResult(resultId, 'Testing 4x4 wide moves...', 'info');
                
                const cube = new CubeState(4);
                const initialState = cube.cubeString;
                
                // Test wide move
                cube.executeWideMove('Rw', true);
                logResult(resultId, 'Rw move executed', 'success');
                
                if (cube.cubeString !== initialState) {
                    logResult(resultId, '✓ Wide move changed cube state', 'success');
                } else {
                    logResult(resultId, '✗ Wide move did not change cube state', 'error');
                }
                
            } catch (error) {
                logResult(resultId, `Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
