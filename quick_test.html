<!DOCTYPE html>
<html>
<head>
    <title>Quick Cube Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { margin: 10px 0; padding: 10px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button { margin: 5px; padding: 10px; }
    </style>
</head>
<body>
    <h1>Quick Cube Test</h1>
    <button onclick="testBasicMoves()">Test Basic Moves</button>
    <button onclick="testScrambleAndSolve()">Test Scramble & Solve</button>
    <div id="results"></div>

    <script src="js/cube-state.js"></script>
    <script>
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            document.getElementById('results').appendChild(div);
        }

        function testBasicMoves() {
            document.getElementById('results').innerHTML = '';
            log('Testing basic moves...', 'info');

            try {
                const cube = new CubeState(3);
                const initial = cube.cubeString;
                
                log(`Initial: ${initial.substring(0, 20)}...`, 'info');

                // Test R R'
                cube.executeMove('R', true);
                const afterR = cube.cubeString;
                log(`After R: ${afterR.substring(0, 20)}...`, 'info');

                cube.executeMove('R', false);
                const afterRPrime = cube.cubeString;
                log(`After R': ${afterRPrime.substring(0, 20)}...`, 'info');

                if (afterRPrime === initial) {
                    log('✓ R R\' sequence works correctly!', 'success');
                } else {
                    log('✗ R R\' sequence failed', 'error');
                    return;
                }

                // Test R4
                cube.executeMove('R', true);
                cube.executeMove('R', true);
                cube.executeMove('R', true);
                cube.executeMove('R', true);

                if (cube.cubeString === initial) {
                    log('✓ R4 sequence works correctly!', 'success');
                } else {
                    log('✗ R4 sequence failed', 'error');
                    return;
                }

                // Test all moves
                const moves = ['R', 'L', 'U', 'D', 'F', 'B'];
                let allPassed = true;

                for (const move of moves) {
                    cube.resetToSolved();
                    const before = cube.cubeString;
                    
                    cube.executeMove(move, true);
                    cube.executeMove(move, false);
                    
                    if (cube.cubeString === before) {
                        log(`✓ ${move} ${move}' works correctly`, 'success');
                    } else {
                        log(`✗ ${move} ${move}' failed`, 'error');
                        allPassed = false;
                    }
                }

                if (allPassed) {
                    log('🎉 All basic moves work correctly!', 'success');
                } else {
                    log('❌ Some moves failed', 'error');
                }

            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }

        async function testScrambleAndSolve() {
            log('Testing scramble and solve...', 'info');

            try {
                const cube = new CubeState(3);
                
                // Generate scramble
                const scramble = cube.generateScramble(10);
                log(`Scramble: ${scramble}`, 'info');
                
                // Apply scramble
                cube.applyScramble(scramble);
                log('Scramble applied', 'success');
                
                if (cube.isSolved()) {
                    log('⚠️ Cube is still solved after scramble', 'error');
                    return;
                }
                
                // Get Kociemba string
                const kociembaString = cube.getKociembaString();
                if (!kociembaString) {
                    log('❌ Could not convert to Kociemba format', 'error');
                    return;
                }
                
                log(`Kociemba: ${kociembaString.substring(0, 20)}...`, 'info');
                
                // Try to solve
                const response = await fetch('http://localhost:5000/solve', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ cube_string: kociembaString })
                });
                
                if (response.ok) {
                    const data = await response.json();
                    if (data.solution) {
                        log(`Solution: ${data.solution}`, 'success');
                        
                        // Apply solution
                        cube.applySolution(data.solution);
                        
                        if (cube.isSolved()) {
                            log('🎉 Cube solved successfully!', 'success');
                        } else {
                            log('❌ Solution did not solve the cube', 'error');
                        }
                    } else {
                        log('❌ No solution received', 'error');
                    }
                } else {
                    log(`❌ Server error: ${response.status}`, 'error');
                }
                
            } catch (error) {
                log(`Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
